{"domain": "sirohi_thin_client", "name": "<PERSON><PERSON>i Thin Client", "version": "0.2.0", "documentation": "https://tea.griffin.ovh/g/thin-client", "issue_tracker": "https://tea.griffin.ovh/g/thin-client/issues", "dependencies": [], "codeowners": ["@griffin"], "requirements": ["aiohttp>=3.8.0"], "iot_class": "cloud_push", "config_flow": true, "after_dependencies": [], "integration_type": "service"}