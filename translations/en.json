{"config": {"step": {"user": {"title": "<PERSON><PERSON><PERSON> Thin Client Setup", "description": "Configure your Sirohi Thin Client connection.\n\nFor setup instructions, visit: {docs_url}", "data": {"url": "Backend URL", "license_key": "License Key"}, "data_description": {"url": "The HTTPS URL of your Sirohi Labs backend endpoint", "license_key": "Your Sirohi Labs license key for authentication"}}}, "error": {"cannot_connect": "Failed to connect to the backend. Please check your URL and network connection.", "invalid_url": "Invalid URL. Please ensure the URL is properly formatted and uses HTTPS.", "invalid_license": "Invalid license key. Please check your license key and try again.", "unknown": "Unexpected error occurred. Please check the logs for more details."}, "abort": {"already_configured": "This backend URL is already configured."}}, "options": {"step": {"init": {"title": "Sirohi Thin Client Options", "description": "Configure advanced options for Sirohi Thin Client.", "data": {"timeout": "Request Timeout (seconds)", "retries": "Maximum Retries"}}}}}