# Sirohi Thin Client Environment Variables
# Copy this file to .env and fill in your actual values

# Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# Backend Configuration (for testing)
BACKEND_URL=https://<BACKEND_HOST>
BACKEND_CERT_FINGERPRINT=optional-sha256-cert-fingerprint-for-pinning

# Home Assistant Configuration (for testing)
HASS_URL=http://localhost:8123
HASS_TOKEN=your-long-lived-access-token

# Development/Testing Configuration
DEBUG=false
LOG_LEVEL=INFO

# Optional: Override default model settings
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_OUTPUT_TOKENS=2048

# Test Configuration
PYTEST_TIMEOUT=30
INTEGRATION_TESTS=false
