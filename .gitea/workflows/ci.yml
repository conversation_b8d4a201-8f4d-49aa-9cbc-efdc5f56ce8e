name: CI Pipeline
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.10, 3.11, 3.12]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt
          pip install -e .

      - name: Lint with ruff
        run: |
          ruff check custom_components/ backend/
          ruff format --check custom_components/ backend/

      - name: Security scan with bandit
        run: |
          bandit -r custom_components/ backend/ -f json -o bandit-report.json || true
          bandit -r custom_components/ backend/

      - name: Type check with mypy
        run: |
          mypy custom_components/ --ignore-missing-imports

      - name: Test with pytest
        run: |
          pytest tests/ --cov=custom_components.sirohi_thin_client --cov-report=xml --cov-report=term-missing

      - name: Backend tests
        run: |
          cd backend && python -m pytest test_gemini_client.py -v

      - name: Security test markers
        run: |
          pytest -m "security" tests/ || echo "No security-specific tests found"

  integration-test:
    runs-on: ubuntu-latest
    needs: test

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: 3.11

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Run E2E tests
        run: |
          pytest -m "e2e" tests/ || echo "E2E tests require backend setup"

      - name: Validate manifest
        run: |
          python -c "import json; json.load(open('manifest.json'))"
          echo "✅ manifest.json is valid"

      - name: Check HACS compatibility
        run: |
          python -c "import json; hacs=json.load(open('hacs.json')); print(f'HACS config valid for {hacs[\"name\"]}')"
