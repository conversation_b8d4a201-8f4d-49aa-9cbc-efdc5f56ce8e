# Development Guide

This guide covers development setup, testing, and contribution guidelines for the Sirohi Thin Client.

## 🛠️ Development Setup

### Prerequisites

- Python 3.11+
- Git
- Home Assistant instance (for integration testing)
- Google Gemini API key (for integration testing)

### 1. Clone and Setup

```bash
# Clone repository
git clone <repository-url>
cd thin-client

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

### 2. Environment Configuration

```bash
# Run interactive setup
python3 setup_env.py

# Or manually copy and edit
cp .env.example .env
# Edit .env with your development configuration
```

**Development Environment Variables:**

```bash
# Gemini API Configuration (optional for mock testing)
GEMINI_API_KEY=your-development-key

# Home Assistant Configuration (for integration testing)
HASS_URL=http://localhost:8123
HASS_TOKEN=your-development-token

# Development Settings
DEBUG=true
LOG_LEVEL=DEBUG
INTEGRATION_TESTS=false  # Set to true for real API testing
```

### 3. Pre-commit Hooks

```bash
# Install pre-commit hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

## 🧪 Testing

### Test Structure

```
tests/
├── test_api.py              # API endpoint tests
├── test_automation.py       # Automation tests
├── test_context.py          # Context collection tests
├── test_entity_control.py   # Entity control tests
├── test_models.py           # Data model tests
└── test_scene.py            # Scene management tests

backend/
└── test_gemini_client.py    # Gemini integration tests
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=backend --cov=tests --cov-report=html

# Run specific test files
pytest tests/test_api.py -v
pytest backend/test_gemini_client.py -v

# Run with specific markers
pytest -m "not integration"     # Skip integration tests
pytest -m "security"           # Security tests only

# Run integration tests (requires real API keys)
INTEGRATION_TESTS=true pytest
```

### Test Categories

**Unit Tests:**
- Mock all external dependencies
- Fast execution (< 1 second per test)
- No network calls

**Integration Tests:**
- Test with real APIs (Gemini, Home Assistant)
- Require valid API keys
- Slower execution

**Security Tests:**
- Input validation
- Error handling
- Sensitive data redaction

### Writing Tests

```python
import pytest
from unittest.mock import Mock, patch
from backend.gemini_client import GeminiClient

class TestGeminiClient:
    """Test Gemini client functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return Mock(api_key="test-key", model="gemini-2.0-flash")
    
    @pytest.mark.asyncio
    async def test_generate_success(self, mock_config):
        """Test successful generation."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            # Test implementation
```

## 🔧 Code Quality

### Linting and Formatting

```bash
# Run ruff linter
ruff check .

# Fix auto-fixable issues
ruff check . --fix

# Format code
ruff format .

# Check specific files
ruff check backend/gemini_client.py
```

### Security Scanning

```bash
# Run bandit security scanner
bandit -r backend/ -f json

# Run with specific confidence level
bandit -r . -lll  # Low, medium, high confidence

# Exclude test files
bandit -r backend/ --exclude backend/test_*.py
```

### Type Checking

```bash
# Run mypy type checker
mypy backend/ --ignore-missing-imports

# Check specific files
mypy backend/gemini_client.py
```

## 🏗️ Architecture

### Project Structure

```
thin-client/
├── backend/                 # Core backend services
│   ├── __init__.py
│   ├── main.py             # FastAPI application
│   ├── gemini_client.py    # Gemini AI integration
│   ├── http_utils.py       # HTTP utilities
│   ├── models.py           # Pydantic models
│   └── test_gemini_client.py
├── tests/                  # Test suite
├── docs/                   # Documentation
├── .env.example           # Environment template
├── requirements.txt       # Production dependencies
├── requirements-dev.txt   # Development dependencies
├── pytest.ini            # Pytest configuration
├── pyproject.toml         # Project configuration
└── .pre-commit-config.yaml
```

### Key Components

**FastAPI Application (`main.py`):**
- REST API endpoints
- Request/response handling
- Error handling and logging

**Gemini Client (`gemini_client.py`):**
- Google Gemini API integration
- Async request handling
- Error mapping and retry logic

**HTTP Utils (`http_utils.py`):**
- HTTPS validation
- Certificate pinning
- Security utilities

### Design Patterns

**Async/Await:**
```python
async def process_request(payload: dict) -> dict:
    """Process request asynchronously."""
    result = await gemini_client.generate(payload)
    return result
```

**Error Handling:**
```python
try:
    result = await api_call()
except SpecificError as e:
    logger.error("Specific error: %s", str(e))
    raise CustomError(f"Operation failed: {e}")
```

**Configuration:**
```python
@dataclass
class Config:
    """Application configuration."""
    api_key: str
    model: str = "gemini-2.0-flash"
    temperature: float = 0.1
```

## 🔄 Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# ... edit files ...

# Run tests
pytest

# Run quality checks
ruff check . --fix
bandit -r backend/

# Commit changes
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Testing Workflow

```bash
# Test during development
pytest tests/test_specific.py::test_function -v

# Test with coverage
pytest --cov=backend --cov-report=term-missing

# Test integration (with real APIs)
INTEGRATION_TESTS=true pytest backend/test_gemini_client.py
```

### 3. Debugging

```bash
# Run with debug logging
DEBUG=true python3 backend/main.py

# Use pytest with debugging
pytest --pdb tests/test_api.py::test_function

# Debug specific test
pytest -s tests/test_api.py::test_function
```

## 📝 Contributing Guidelines

### Code Style

- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write comprehensive docstrings
- Keep functions small and focused

### Commit Messages

Use conventional commit format:

```
feat: add new feature
fix: resolve bug in authentication
docs: update API documentation
test: add integration tests
refactor: improve error handling
```

### Pull Request Process

1. **Fork and Branch**: Create feature branch from main
2. **Develop**: Implement changes with tests
3. **Quality**: Run linting, formatting, and security checks
4. **Test**: Ensure all tests pass
5. **Document**: Update documentation if needed
6. **Submit**: Create pull request with clear description

### Code Review Checklist

- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Security scan passes
- [ ] Documentation updated
- [ ] Breaking changes documented
- [ ] Performance impact considered

## 🐛 Debugging

### Common Issues

**Import Errors:**
```bash
# Ensure virtual environment is activated
source venv/bin/activate

# Install in development mode
pip install -e .
```

**Test Failures:**
```bash
# Run specific test with verbose output
pytest tests/test_api.py::test_health -v -s

# Debug with pdb
pytest --pdb tests/test_api.py::test_health
```

**API Integration Issues:**
```bash
# Test API key
python3 validate_api_key.py

# Check environment variables
python3 -c "import os; print(os.getenv('GEMINI_API_KEY'))"
```

### Logging

```python
import logging

# Configure logging for development
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
logger.debug("Debug message")
```

## 🚀 Release Process

### Version Management

```bash
# Update version in pyproject.toml
# Update CHANGELOG.md
# Create release tag
git tag -a v1.0.0 -m "Release v1.0.0"
git push origin v1.0.0
```

### Release Checklist

- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version bumped
- [ ] Changelog updated
- [ ] Security scan clean
- [ ] Performance tested
- [ ] Deployment tested

## 📚 Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Pytest Documentation](https://docs.pytest.org/)
- [Google Gemini API](https://ai.google.dev/docs)
- [Home Assistant API](https://developers.home-assistant.io/docs/api/rest/)

---

For deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).
For security guidelines, see [SECURITY.md](../SECURITY.md).
