# API Documentation

Complete API reference for the Sirohi Thin Client.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

## Authentication

The API uses environment-based configuration. No authentication headers are required for the client endpoints, but the backend requires proper configuration of:

- `GEMINI_API_KEY`: Google Gemini API key
- `HASS_TOKEN`: Home Assistant long-lived access token

## Content Type

All requests and responses use `application/json` content type.

## Error Handling

All endpoints return structured error responses:

```json
{
  "error": "Error description",
  "details": "Additional error details",
  "status_code": 400
}
```

## Endpoints

### Health Check

**GET** `/health`

Check system health and status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "services": {
    "gemini": "connected",
    "home_assistant": "connected"
  }
}
```

**Status Codes:**
- `200`: System healthy
- `503`: System unhealthy

---

### Process Natural Language

**POST** `/process`

Process natural language commands using Gemini AI.

**Request Body:**
```json
{
  "prompt": "Turn on the kitchen lights",
  "context": {
    "entities": [
      {
        "entity_id": "light.kitchen",
        "state": "off",
        "attributes": {
          "friendly_name": "Kitchen Light"
        }
      }
    ],
    "areas": ["Kitchen", "Living Room"],
    "scenes": [
      {
        "name": "Movie Night",
        "entity_id": "scene.movie_night"
      }
    ]
  }
}
```

**Response:**
```json
{
  "success": true,
  "response": {
    "action": "call_service",
    "data": {
      "domain": "light",
      "service": "turn_on",
      "entity_id": "light.kitchen"
    }
  },
  "metadata": {
    "model": "gemini-2.0-flash",
    "finish_reason": "STOP",
    "safety_ratings": []
  }
}
```

**Status Codes:**
- `200`: Success
- `400`: Invalid request
- `429`: Rate limited
- `500`: Server error

---

### Get Context

**GET** `/context`

Retrieve current Home Assistant context including entities, areas, and scenes.

**Query Parameters:**
- `include_entities` (boolean): Include entity list (default: true)
- `include_areas` (boolean): Include area list (default: true)
- `include_scenes` (boolean): Include scene list (default: true)

**Response:**
```json
{
  "entities": [
    {
      "entity_id": "light.kitchen",
      "state": "off",
      "attributes": {
        "friendly_name": "Kitchen Light",
        "supported_features": 1
      }
    }
  ],
  "areas": [
    {
      "area_id": "kitchen",
      "name": "Kitchen"
    }
  ],
  "scenes": [
    {
      "entity_id": "scene.movie_night",
      "name": "Movie Night",
      "state": "scening"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200`: Success
- `503`: Home Assistant unavailable

---

### Toggle Entity

**POST** `/entity/{entity_id}/toggle`

Toggle the state of a specific entity.

**Path Parameters:**
- `entity_id` (string): The entity ID to toggle

**Response:**
```json
{
  "success": true,
  "entity_id": "light.kitchen",
  "previous_state": "off",
  "new_state": "on",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200`: Success
- `404`: Entity not found
- `400`: Entity not toggleable
- `503`: Home Assistant unavailable

---

### Activate Scene

**POST** `/scene/{scene_id}/activate`

Activate a specific scene.

**Path Parameters:**
- `scene_id` (string): The scene entity ID to activate

**Response:**
```json
{
  "success": true,
  "scene_id": "scene.movie_night",
  "activated": true,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200`: Success
- `404`: Scene not found
- `503`: Home Assistant unavailable

---

### List Automations

**GET** `/automations`

List all available automations.

**Response:**
```json
{
  "automations": [
    {
      "entity_id": "automation.morning_routine",
      "alias": "Morning Routine",
      "state": "on",
      "last_triggered": "2024-01-01T06:00:00Z"
    }
  ],
  "count": 1
}
```

**Status Codes:**
- `200`: Success
- `503`: Home Assistant unavailable

---

### Trigger Automation

**POST** `/automation/{automation_id}/trigger`

Trigger a specific automation.

**Path Parameters:**
- `automation_id` (string): The automation entity ID to trigger

**Response:**
```json
{
  "success": true,
  "automation_id": "automation.morning_routine",
  "triggered": true,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200`: Success
- `404`: Automation not found
- `503`: Home Assistant unavailable

## Data Models

### Entity Model

```json
{
  "entity_id": "string",
  "state": "string",
  "attributes": {
    "friendly_name": "string",
    "supported_features": "number"
  }
}
```

### Area Model

```json
{
  "area_id": "string",
  "name": "string"
}
```

### Scene Model

```json
{
  "entity_id": "string",
  "name": "string",
  "state": "string"
}
```

### Process Request Model

```json
{
  "prompt": "string (required)",
  "context": {
    "entities": ["Entity[]"],
    "areas": ["string[]"],
    "scenes": ["Scene[]"]
  }
}
```

### Process Response Model

```json
{
  "success": "boolean",
  "response": {
    "action": "string",
    "data": "object"
  },
  "metadata": {
    "model": "string",
    "finish_reason": "string",
    "safety_ratings": "array"
  }
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Process endpoint**: 10 requests per minute per IP
- **Other endpoints**: 60 requests per minute per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid credentials |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limited |
| 500 | Internal Server Error |
| 503 | Service Unavailable - External service down |

## Examples

### Complete Workflow Example

```bash
# 1. Check system health
curl "http://localhost:8000/health"

# 2. Get current context
curl "http://localhost:8000/context"

# 3. Process natural language command
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Turn on the kitchen lights and set them to 50% brightness",
    "context": {
      "entities": [
        {
          "entity_id": "light.kitchen",
          "state": "off",
          "attributes": {
            "friendly_name": "Kitchen Light",
            "supported_features": 1
          }
        }
      ],
      "areas": ["Kitchen"]
    }
  }'

# 4. Toggle entity directly
curl -X POST "http://localhost:8000/entity/light.kitchen/toggle"

# 5. Activate scene
curl -X POST "http://localhost:8000/scene/scene.movie_night/activate"
```

## WebSocket Support

*Note: WebSocket support is planned for future releases to enable real-time updates.*

## Versioning

The API uses semantic versioning. Current version: `v1.0.0`

Version information is available in:
- Health endpoint response
- HTTP headers: `X-API-Version`

---

For more information, see the [Development Guide](DEVELOPMENT.md) and [Deployment Guide](DEPLOYMENT.md).
