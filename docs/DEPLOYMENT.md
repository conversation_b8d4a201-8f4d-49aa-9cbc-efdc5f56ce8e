# Deployment Guide

This guide covers production deployment of the Sirohi Thin Client.

## 🚀 Production Deployment

### Prerequisites

- Python 3.11+
- SSL/TLS certificates
- Google Gemini API key
- Home Assistant instance with API access
- Reverse proxy (nginx/Apache) recommended

### 1. Server Setup

```bash
# Create dedicated user
sudo useradd -m -s /bin/bash thin-client
sudo su - thin-client

# Clone repository
git clone <repository-url> /home/<USER>/app
cd /home/<USER>/app

# Create virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit with production values
nano .env
```

**Production Environment Variables:**

```bash
# Gemini API Configuration
GEMINI_API_KEY=your-production-gemini-key
GEMINI_MODEL=gemini-2.0-flash
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_OUTPUT_TOKENS=2048

# Home Assistant Configuration
HASS_URL=https://your-homeassistant.domain.com
HASS_TOKEN=your-long-lived-access-token

# Backend Configuration
BACKEND_URL=https://your-backend.domain.com
BACKEND_CERT_FINGERPRINT=sha256-fingerprint-for-cert-pinning

# Production Settings
DEBUG=false
LOG_LEVEL=INFO
PYTEST_TIMEOUT=30
INTEGRATION_TESTS=false
```

### 3. SSL/TLS Setup

**Option A: Let's Encrypt (Recommended)**

```bash
# Install certbot
sudo apt install certbot

# Generate certificates
sudo certbot certonly --standalone -d your-domain.com

# Certificates will be in:
# /etc/letsencrypt/live/your-domain.com/
```

**Option B: Self-Signed (Development Only)**

```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
```

### 4. Systemd Service

Create `/etc/systemd/system/thin-client.service`:

```ini
[Unit]
Description=Sirohi Thin Client
After=network.target

[Service]
Type=exec
User=thin-client
Group=thin-client
WorkingDirectory=/home/<USER>/app
Environment=PATH=/home/<USER>/app/venv/bin
ExecStart=/home/<USER>/app/venv/bin/uvicorn backend.main:app --host 127.0.0.1 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/app

[Install]
WantedBy=multi-user.target
```

**Enable and start service:**

```bash
sudo systemctl daemon-reload
sudo systemctl enable thin-client
sudo systemctl start thin-client
sudo systemctl status thin-client
```

### 5. Reverse Proxy (Nginx)

Create `/etc/nginx/sites-available/thin-client`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Proxy to application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
}
```

**Enable site:**

```bash
sudo ln -s /etc/nginx/sites-available/thin-client /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY backend/ ./backend/
COPY tests/ ./tests/
COPY .env.example ./

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000

CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  thin-client:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - HASS_URL=${HASS_URL}
      - HASS_TOKEN=${HASS_TOKEN}
      - DEBUG=false
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - thin-client
    restart: unless-stopped
```

### Build and Deploy

```bash
# Build image
docker build -t thin-client .

# Run with docker-compose
docker-compose up -d

# View logs
docker-compose logs -f thin-client
```

## 🔧 Configuration Management

### Environment Variables

Use a secrets management system in production:

```bash
# Using Docker secrets
echo "your-api-key" | docker secret create gemini_api_key -

# Using Kubernetes secrets
kubectl create secret generic thin-client-secrets \
  --from-literal=gemini-api-key=your-key \
  --from-literal=hass-token=your-token
```

### Certificate Pinning

Generate certificate fingerprint:

```bash
# Get certificate fingerprint
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -fingerprint -sha256 -noout

# Add to .env
BACKEND_CERT_FINGERPRINT=sha256:AB:CD:EF:...
```

## 📊 Monitoring

### Health Checks

```bash
# Basic health check
curl https://your-domain.com/health

# Detailed status
curl https://your-domain.com/health | jq
```

### Logging

Configure structured logging:

```python
# In production, use JSON logging
LOG_FORMAT=json
LOG_LEVEL=INFO
```

### Metrics

Monitor key metrics:
- Response times
- Error rates
- Gemini API usage
- Memory/CPU usage

## 🔄 Updates

### Rolling Updates

```bash
# Pull latest code
git pull origin main

# Install dependencies
pip install -r requirements.txt

# Run tests
pytest

# Restart service
sudo systemctl restart thin-client
```

### Zero-Downtime Updates

Use blue-green deployment or rolling updates with multiple instances.

## 🚨 Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
sudo journalctl -u thin-client -f

# Check configuration
python3 -c "from backend.main import app; print('Config OK')"
```

**SSL/TLS issues:**
```bash
# Test certificate
openssl s_client -connect your-domain.com:443

# Check nginx config
sudo nginx -t
```

**API connectivity:**
```bash
# Test Gemini API
python3 validate_api_key.py

# Test Home Assistant
curl -H "Authorization: Bearer $HASS_TOKEN" $HASS_URL/api/
```

## 📋 Production Checklist

- [ ] SSL/TLS certificates configured
- [ ] Environment variables set
- [ ] Firewall rules configured
- [ ] Monitoring setup
- [ ] Backup strategy implemented
- [ ] Log rotation configured
- [ ] Security headers enabled
- [ ] Health checks working
- [ ] Documentation updated
- [ ] Team trained on deployment

---

For additional support, see [SECURITY.md](SECURITY.md) and [README.md](../README.md).
