import asyncio
import logging
import secrets
from typing import Any

import aiohttp

_LOGGER = logging.getLogger(__name__)

def _get_secure_jitter(base_delay: float) -> float:
    """Generate secure random jitter using secrets.SystemRandom()."""
    rng = secrets.SystemRandom()
    return base_delay + rng.uniform(0, base_delay * 0.5)

async def _http_post_with_retry(
    url: str,
    payload: dict[str, Any],
    headers: dict[str, str],
    max_retries: int = 3,
    base_delay: float = 1.0,
    timeout_seconds: int = 30
) -> dict[str, Any]:
    """
    HTTP POST with exponential backoff retry and secure jitter.

    Args:
        url: Target URL (must be HTTPS)
        payload: JSON payload to send
        headers: HTTP headers (sensitive headers will be redacted in logs)
        max_retries: Maximum number of retry attempts
        base_delay: Base delay between retries in seconds
        timeout_seconds: Request timeout in seconds

    Returns:
        JSON response as dictionary

    Raises:
        Exception: On final failure after all retries
    """
    for attempt in range(max_retries):
        try:
            timeout = aiohttp.ClientTimeout(total=timeout_seconds)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers, ssl=True) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:
                        retry_after = int(response.headers.get('Retry-After', base_delay))
                        _LOGGER.warning("Rate limited, retrying after %d seconds", retry_after)
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        error_text = await response.text()
                        raise Exception(f"POST {url} returned {response.status}: {error_text}")
        except Exception as e:
            if attempt == max_retries - 1:
                _LOGGER.error("POST failed after %d attempts: %s", max_retries, str(e))
                raise

            # Use secure jitter for retry delay
            jittered_delay = _get_secure_jitter(base_delay * (2 ** attempt))
            _LOGGER.debug("Retrying in %.2f seconds (attempt %d/%d)", jittered_delay, attempt + 1, max_retries)
            await asyncio.sleep(jittered_delay)
