"""FastAPI Backend for Sirohi Thin Client v0.2"""
import logging
from typing import Any

from fastapi import Depends, <PERSON>AP<PERSON>, <PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, ValidationError

from .gemini_client import (
    FatalGeminiError,
    GeminiError,
    RetryableGeminiError,
    generate_response,
)
from .models import (
    AutomationData,
    DeleteData,
    EditData,
    EmptyData,
    SceneData,
    ServiceCallData,
    ThinClientResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
_LOGGER = logging.getLogger(__name__)

app = FastAPI(
    title="Sirohi Thin Client Backend API",
    version="0.2",
    description="Backend API for processing Sirohi Thin Client requests"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def validate_license_key(license_key: str | None = None) -> str:
    """Validate license key from headers."""
    if not license_key:
        raise HTTPException(status_code=401, detail="License key required")
    return license_key

@app.post("/create_automation", response_model=ThinClientResponse)
async def create_automation_endpoint(
    data: AutomationData,
    license_key: str = Depends(validate_license_key)
):
    """Create a new automation."""
    try:
        # Process automation creation logic here
        # This would integrate with your AI/NLP processing

        response_data = {
            "automation_id": data.alias.lower().replace(" ", "_"),
            "alias": data.alias,
            "status": "created"
        }

        return ThinClientResponse(
            success=True,
            message=f"Automation '{data.alias}' created successfully",
            data=response_data,
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error creating automation: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to create automation",
            error_code="CREATION_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/edit_automation", response_model=ThinClientResponse)
async def edit_automation_endpoint(
    data: EditData,
    license_key: str = Depends(validate_license_key)
):
    """Edit an existing automation."""
    try:
        response_data = {
            "automation_id": data.id,
            "status": "updated",
            "updates_applied": data.updates
        }

        return ThinClientResponse(
            success=True,
            message=f"Automation '{data.id}' updated successfully",
            data=response_data,
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error editing automation: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to edit automation",
            error_code="EDIT_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/delete_automation", response_model=ThinClientResponse)
async def delete_automation_endpoint(
    data: DeleteData,
    license_key: str = Depends(validate_license_key)
):
    """Delete an automation."""
    try:
        return ThinClientResponse(
            success=True,
            message=f"Automation '{data.id}' deleted successfully",
            data={"automation_id": data.id, "status": "deleted"},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error deleting automation: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to delete automation",
            error_code="DELETE_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/list_automations", response_model=ThinClientResponse)
async def list_automations_endpoint(
    data: EmptyData,
    license_key: str = Depends(validate_license_key)
):
    """List all automations."""
    try:
        # Mock data - replace with actual automation listing logic
        automations = [
            {"id": "morning_lights", "alias": "Morning Lights", "enabled": True},
            {"id": "evening_routine", "alias": "Evening Routine", "enabled": False}
        ]

        return ThinClientResponse(
            success=True,
            message="Automations retrieved successfully",
            data={"automations": automations, "count": len(automations)},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error listing automations: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to list automations",
            error_code="LIST_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/create_scene", response_model=ThinClientResponse)
async def create_scene_endpoint(
    data: SceneData,
    license_key: str = Depends(validate_license_key)
):
    """Create a new scene."""
    try:
        response_data = {
            "scene_id": data.name.lower().replace(" ", "_"),
            "name": data.name,
            "entities": list(data.entities.keys()),
            "status": "created"
        }

        return ThinClientResponse(
            success=True,
            message=f"Scene '{data.name}' created successfully",
            data=response_data,
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error creating scene: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to create scene",
            error_code="CREATION_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/edit_scene", response_model=ThinClientResponse)
async def edit_scene_endpoint(
    data: EditData,
    license_key: str = Depends(validate_license_key)
):
    """Edit an existing scene."""
    try:
        return ThinClientResponse(
            success=True,
            message=f"Scene '{data.id}' updated successfully",
            data={"scene_id": data.id, "status": "updated"},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error editing scene: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to edit scene",
            error_code="EDIT_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/delete_scene", response_model=ThinClientResponse)
async def delete_scene_endpoint(
    data: DeleteData,
    license_key: str = Depends(validate_license_key)
):
    """Delete a scene."""
    try:
        return ThinClientResponse(
            success=True,
            message=f"Scene '{data.id}' deleted successfully",
            data={"scene_id": data.id, "status": "deleted"},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error deleting scene: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to delete scene",
            error_code="DELETE_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/activate_scene", response_model=ThinClientResponse)
async def activate_scene_endpoint(
    data: DeleteData,  # Uses same structure as delete (just ID)
    license_key: str = Depends(validate_license_key)
):
    """Activate a scene."""
    try:
        return ThinClientResponse(
            success=True,
            message=f"Scene '{data.id}' activated successfully",
            data={"scene_id": data.id, "status": "activated"},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error activating scene: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to activate scene",
            error_code="ACTIVATION_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/list_scenes", response_model=ThinClientResponse)
async def list_scenes_endpoint(
    data: EmptyData,
    license_key: str = Depends(validate_license_key)
):
    """List all scenes."""
    try:
        # Mock data - replace with actual scene listing logic
        scenes = [
            {"id": "movie_night", "name": "Movie Night", "entities": 3},
            {"id": "dinner_party", "name": "Dinner Party", "entities": 5}
        ]

        return ThinClientResponse(
            success=True,
            message="Scenes retrieved successfully",
            data={"scenes": scenes, "count": len(scenes)},
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error listing scenes: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to list scenes",
            error_code="LIST_ERROR",
            error_details={"error": str(e)},
            data=None
        )

@app.post("/call_service", response_model=ThinClientResponse)
async def call_service_endpoint(
    data: ServiceCallData,
    license_key: str = Depends(validate_license_key)
):
    """Call a Home Assistant service."""
    try:
        response_data = {
            "service": f"{data.domain}.{data.service}",
            "entity_id": data.entity_id,
            "status": "called"
        }

        return ThinClientResponse(
            success=True,
            message=f"Service {data.domain}.{data.service} called successfully",
            data=response_data,
            error_code=None,
            error_details=None
        )

    except Exception as e:
        _LOGGER.error("Error calling service: %s", str(e))
        return ThinClientResponse(
            success=False,
            message="Failed to call service",
            error_code="SERVICE_ERROR",
            error_details={"error": str(e)},
            data=None
        )

class GenerateRequest(BaseModel):
    """Request model for /generate endpoint."""
    prompt: str
    user_id: str | None = None
    model: str | None = None
    context: dict[str, Any] | None = None

class GenerateResponse(BaseModel):
    """Response model for /generate endpoint."""
    success: bool
    response: dict[str, Any] | None = None
    metadata: dict[str, Any] | None = None
    error: str | None = None
    error_code: str | None = None

@app.post("/generate", response_model=GenerateResponse)
async def generate_endpoint(
    request: GenerateRequest,
    x_license_key: str | None = Header(None, alias="X-License-Key")
):
    """Generate AI response using Gemini 2.5 Flash."""
    try:
        # Validate license key
        if not x_license_key:
            raise HTTPException(status_code=401, detail="X-License-Key header required")

        # Prepare prompt for Gemini
        prompt_json = {
            "prompt": request.prompt,
            "context": request.context or {},
            "user_id": request.user_id,
            "model": request.model
        }

        _LOGGER.info("Processing generate request for user: %s", request.user_id or "anonymous")

        # Generate response using Gemini
        result = await generate_response(prompt_json)

        return GenerateResponse(
            success=True,
            response=result.get("response"),
            metadata=result.get("metadata")
        )

    except RetryableGeminiError as e:
        _LOGGER.warning("Retryable Gemini error: %s", str(e))
        raise HTTPException(
            status_code=e.status_code,
            detail=f"Service temporarily unavailable: {str(e)}",
            headers={"Retry-After": str(e.retry_after or 60)}
        )

    except FatalGeminiError as e:
        _LOGGER.error("Fatal Gemini error: %s", str(e))
        return GenerateResponse(
            success=False,
            error=str(e),
            error_code="GEMINI_ERROR"
        )

    except GeminiError as e:
        _LOGGER.error("Unexpected Gemini error: %s", str(e))
        return GenerateResponse(
            success=False,
            error="AI service error",
            error_code="AI_ERROR"
        )

    except Exception as e:
        _LOGGER.error("Unexpected error in generate endpoint: %s", str(e))
        return GenerateResponse(
            success=False,
            error="Internal server error",
            error_code="INTERNAL_ERROR"
        )

@app.exception_handler(ValidationError)
async def validation_exception_handler(request, exc):
    """Handle Pydantic validation errors."""
    return ThinClientResponse(
        success=False,
        message="Validation error",
        error_code="VALIDATION_ERROR",
        error_details={"errors": exc.errors()},
        data=None
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return ThinClientResponse(
        success=False,
        message=exc.detail,
        error_code="HTTP_ERROR",
        error_details={"status_code": exc.status_code},
        data=None
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
