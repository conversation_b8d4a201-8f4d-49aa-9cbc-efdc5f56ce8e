{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Sirohi Thin Client API Schema", "version": "0.2", "type": "object", "properties": {"action": {"type": "string", "enum": ["create_automation", "edit_automation", "delete_automation", "list_automations", "create_scene", "edit_scene", "delete_scene", "activate_scene", "list_scenes", "call_service"], "description": "The action to perform on the Home Assistant instance"}, "data": {"type": "object", "description": "Action-specific data payload", "oneOf": [{"title": "AutomationData", "properties": {"alias": {"type": "string"}, "description": {"type": "string"}, "trigger": {"type": "object"}, "condition": {"type": "object"}, "action": {"type": "object"}, "mode": {"type": "string", "enum": ["single", "restart", "queued", "parallel"]}}, "required": ["alias", "trigger", "action"]}, {"title": "SceneData", "properties": {"name": {"type": "string"}, "entities": {"type": "object"}, "snapshot_entities": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "entities"]}, {"title": "ServiceCallData", "properties": {"domain": {"type": "string"}, "service": {"type": "string"}, "entity_id": {"type": ["string", "array"]}, "data": {"type": "object"}}, "required": ["domain", "service"]}, {"title": "EditData", "properties": {"id": {"type": "string"}, "updates": {"type": "object"}}, "required": ["id", "updates"]}, {"title": "DeleteData", "properties": {"id": {"type": "string"}}, "required": ["id"]}, {"title": "EmptyData", "properties": {}, "additionalProperties": false}]}, "context": {"type": "object", "description": "Full Home Assistant context", "properties": {"entities": {"type": "array"}, "devices": {"type": "array"}, "areas": {"type": "array"}, "scenes": {"type": "array"}, "zones": {"type": "array"}}}}, "required": ["action", "data"], "additionalProperties": false, "examples": [{"action": "create_automation", "data": {"alias": "Morning Lights", "trigger": {"platform": "time", "at": "07:00:00"}, "action": {"service": "light.turn_on", "entity_id": "light.living_room"}}}, {"action": "create_scene", "data": {"name": "Movie Night", "entities": {"light.living_room": {"state": "on", "brightness": 30}, "light.kitchen": {"state": "off"}}}}, {"action": "call_service", "data": {"domain": "light", "service": "turn_on", "entity_id": "light.bedroom", "data": {"brightness": 255, "color_name": "blue"}}}]}