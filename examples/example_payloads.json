{"examples": [{"title": "Successful Automation Creation", "description": "Valid payload for creating a morning lights automation", "payload": {"action": "create_automation", "data": {"alias": "Morning Lights", "description": "Turn on living room lights every morning at 7 AM", "trigger": {"platform": "time", "at": "07:00:00"}, "condition": {"condition": "state", "entity_id": "binary_sensor.workday_sensor", "state": "on"}, "action": {"service": "light.turn_on", "target": {"entity_id": "light.living_room"}, "data": {"brightness": 255, "color_temp": 370}}, "mode": "single"}, "context": {"entities": [{"entity_id": "light.living_room", "state": "off", "attributes": {"brightness": 0, "friendly_name": "Living Room Light"}}], "devices": ["Smart Light Switch"], "areas": ["Living Room"], "scenes": [], "zones": []}}, "expected_response": {"success": true, "action": "create_automation", "result": {"status": "created", "automation_id": "Morning Lights"}}}, {"title": "Validation Error - Missing Required Fields", "description": "Invalid payload missing required automation fields", "payload": {"action": "create_automation", "data": {"alias": "Incomplete Automation"}, "context": {"entities": [], "devices": [], "areas": [], "scenes": [], "zones": []}}, "expected_response": {"success": false, "error": "Missing required fields for automation creation", "error_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"title": "Unknown Action Error", "description": "Invalid payload with unrecognized action", "payload": {"action": "invalid_action_name", "data": {"some_field": "some_value"}, "context": {"entities": [], "devices": [], "areas": [], "scenes": [], "zones": []}}, "expected_response": {"success": false, "error": "Unknown action: invalid_action_name", "error_type": "UnknownActionError"}}], "additional_examples": {"scene_creation": {"payload": {"action": "create_scene", "data": {"name": "Movie Night", "entities": {"light.living_room": {"state": "on", "brightness": 30, "color_name": "red"}, "light.kitchen": {"state": "off"}, "media_player.tv": {"state": "on", "volume_level": 0.3}}}}}, "service_call": {"payload": {"action": "call_service", "data": {"domain": "light", "service": "turn_on", "entity_id": ["light.bedroom", "light.hallway"], "data": {"brightness": 200, "color_temp": 400}}}}, "edit_automation": {"payload": {"action": "edit_automation", "data": {"id": "morning_lights", "updates": {"trigger": {"platform": "time", "at": "06:30:00"}}}}}, "delete_scene": {"payload": {"action": "delete_scene", "data": {"id": "movie_night"}}}, "list_automations": {"payload": {"action": "list_automations", "data": {}}}}, "curl_examples": [{"description": "Create automation with curl", "command": "curl -X POST 'https://backend.sirohilabs.com/create_automation' \\\n  -H 'Content-Type: application/json' \\\n  -H 'X-License-Key: your-license-key-here' \\\n  -d '{\n    \"alias\": \"Evening Routine\",\n    \"trigger\": {\n      \"platform\": \"sun\",\n      \"event\": \"sunset\"\n    },\n    \"action\": {\n      \"service\": \"scene.turn_on\",\n      \"target\": {\n        \"entity_id\": \"scene.evening_lights\"\n      }\n    }\n  }'"}, {"description": "Call service with curl", "command": "curl -X POST 'https://<BACKEND_HOST>/call_service' \\\n  -H 'Content-Type: application/json' \\\n  -H 'X-License-Key: your-license-key-here' \\\n  -d '{\n    \"domain\": \"climate\",\n    \"service\": \"set_temperature\",\n    \"entity_id\": \"climate.living_room\",\n    \"data\": {\n      \"temperature\": 22\n    }\n  }'"}], "schema_info": {"version": "0.2", "supported_actions": ["create_automation", "edit_automation", "delete_automation", "list_automations", "create_scene", "edit_scene", "delete_scene", "activate_scene", "list_scenes", "call_service"], "required_headers": ["X-License-Key"], "base_url": "https://<BACKEND_HOST>"}}