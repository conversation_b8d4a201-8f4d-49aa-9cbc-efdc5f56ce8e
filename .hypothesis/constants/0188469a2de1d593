# file: /Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/backend/gemini_client.py
# hypothesis_version: 6.135.26

[0.1, 400, 401, 429, 500, 2048, '. ', '401', '429', '500', 'GEMINI_API_KEY', 'MAX_TOKENS', 'Retry-After', 'SAFETY', 'STOP', 'UNKNOWN', 'USE_MOCK_GEMINI', 'action', 'areas', 'code', 'context', 'entities', 'entity_id', 'false', 'finish_reason', 'gemini-2.0-flash', 'headers', 'internalservererror', 'metadata', 'mock-key-for-testing', 'model', 'name', 'prompt', 'rate limit', 'resourceexhausted', 'response', 'retry-after', 'safety', 'safety_ratings', 'scenes', 'server error', 'state', 'success', 'test-api-key', 'text', 'text_response', 'true', 'unauthenticated', 'unauthorized']