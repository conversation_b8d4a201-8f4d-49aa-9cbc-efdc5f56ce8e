# file: /Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/backend/gemini_client.py
# hypothesis_version: 6.135.26

[0.1, 400, 401, 429, 500, 2048, '. ', '401', '429', '500', 'GEMINI_API_KEY', 'MAX_TOKENS', 'Retry-After', 'SAFETY', 'STOP', 'UNKNOWN', 'action', 'areas', 'code', 'context', 'entities', 'entity_id', 'finish_reason', 'gemini-2.0-flash-exp', 'headers', 'internalservererror', 'metadata', 'model', 'name', 'prompt', 'rate limit', 'resourceexhausted', 'response', 'retry-after', 'safety', 'safety_ratings', 'scenes', 'server error', 'state', 'success', 'text', 'text_response', 'unauthenticated', 'unauthorized']