# file: /Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/context.py
# hypothesis_version: 6.135.26

['Collected %d scenes', 'Collected %d zones', '[REDACTED]', 'api_key', 'areas', 'attributes', 'auth', 'automation', 'automations', 'credential', 'devices', 'entities', 'entity_id', 'friendly_name', 'latitude', 'license', 'license_key', 'longitude', 'name', 'password', 'private', 'radius', 'scene', 'scenes', 'secret', 'state', 'token', 'zone', 'zones']