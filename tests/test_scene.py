"""Tests for scene.py module."""
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

from scene import activate_scene, create_scene, delete_scene, edit_scene, list_scenes


@pytest.fixture
def mock_hass():
    """Mock Home Assistant instance."""
    hass = Mock(spec=HomeAssistant)
    hass.services = Mock()
    hass.services.async_call = AsyncMock()
    hass.states = Mock()
    return hass

@pytest.fixture
def sample_scene_data():
    """Sample scene data for testing."""
    return {
        "name": "Test Scene",
        "entities": {
            "light.living_room": {"state": "on", "brightness": 255},
            "light.bedroom": {"state": "off"}
        }
    }

class TestCreateScene:
    """Test create_scene function."""

    @pytest.mark.asyncio
    async def test_create_scene_success(self, mock_hass, sample_scene_data):
        """Test successful scene creation."""
        result = await create_scene(mock_hass, sample_scene_data)

        assert result["status"] == "created"
        assert result["scene_id"] == "Test Scene"
        mock_hass.services.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_scene_missing_name(self, mock_hass):
        """Test scene creation with missing name."""
        invalid_data = {"entities": {}}

        with pytest.raises(ValueError) as exc_info:
            await create_scene(mock_hass, invalid_data)

        assert "Scene name is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_scene_missing_entities(self, mock_hass):
        """Test scene creation with missing entities."""
        invalid_data = {"name": "Test Scene"}

        with pytest.raises(ValueError) as exc_info:
            await create_scene(mock_hass, invalid_data)

        assert "Scene entities are required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_scene_ha_error(self, mock_hass, sample_scene_data):
        """Test handling of Home Assistant errors."""
        mock_hass.services.async_call.side_effect = HomeAssistantError("Service error")

        with pytest.raises(HomeAssistantError) as exc_info:
            await create_scene(mock_hass, sample_scene_data)

        assert "Home Assistant error creating scene" in str(exc_info.value)

class TestEditScene:
    """Test edit_scene function."""

    @pytest.mark.asyncio
    async def test_edit_scene_success(self, mock_hass):
        """Test successful scene editing."""
        # Mock existing scene
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        updates = {"entities": {"light.kitchen": {"state": "on"}}}
        result = await edit_scene(mock_hass, "test_scene", updates)

        assert result["status"] == "updated"
        assert result["scene_id"] == "test_scene"
        assert result["updates"] == updates
        mock_hass.services.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_edit_scene_not_found(self, mock_hass):
        """Test editing non-existent scene."""
        mock_hass.states.get.return_value = None

        with pytest.raises(HomeAssistantError) as exc_info:
            await edit_scene(mock_hass, "nonexistent", {})

        assert "Scene not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_edit_scene_empty_id(self, mock_hass):
        """Test editing with empty scene ID."""
        with pytest.raises(ValueError) as exc_info:
            await edit_scene(mock_hass, "", {})

        assert "Scene ID is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_edit_scene_no_entities_update(self, mock_hass):
        """Test editing scene without entities update."""
        # Mock existing scene
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        updates = {"name": "New Name"}  # No entities
        result = await edit_scene(mock_hass, "test_scene", updates)

        assert result["status"] == "updated"
        # Should not call service if no entities to update
        mock_hass.services.async_call.assert_not_called()

class TestDeleteScene:
    """Test delete_scene function."""

    @pytest.mark.asyncio
    async def test_delete_scene_success(self, mock_hass):
        """Test successful scene deletion."""
        # Mock existing scene
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        result = await delete_scene(mock_hass, "test_scene")

        assert result["status"] == "deleted"
        assert result["scene_id"] == "test_scene"
        mock_hass.services.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_scene_not_found(self, mock_hass):
        """Test deleting non-existent scene."""
        mock_hass.states.get.return_value = None

        with pytest.raises(HomeAssistantError) as exc_info:
            await delete_scene(mock_hass, "nonexistent")

        assert "Scene not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_scene_empty_id(self, mock_hass):
        """Test deleting with empty scene ID."""
        with pytest.raises(ValueError) as exc_info:
            await delete_scene(mock_hass, "")

        assert "Scene ID is required" in str(exc_info.value)

class TestActivateScene:
    """Test activate_scene function."""

    @pytest.mark.asyncio
    async def test_activate_scene_success(self, mock_hass):
        """Test successful scene activation."""
        # Mock existing scene
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        result = await activate_scene(mock_hass, "test_scene")

        assert result["status"] == "activated"
        assert result["scene_id"] == "test_scene"
        mock_hass.services.async_call.assert_called_once_with(
            "scene", "turn_on", {"entity_id": "scene.test_scene"}, blocking=True
        )

    @pytest.mark.asyncio
    async def test_activate_scene_not_found(self, mock_hass):
        """Test activating non-existent scene."""
        mock_hass.states.get.return_value = None

        with pytest.raises(HomeAssistantError) as exc_info:
            await activate_scene(mock_hass, "nonexistent")

        assert "Scene not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_activate_scene_empty_id(self, mock_hass):
        """Test activating with empty scene ID."""
        with pytest.raises(ValueError) as exc_info:
            await activate_scene(mock_hass, "")

        assert "Scene ID is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_activate_scene_ha_error(self, mock_hass):
        """Test handling of Home Assistant errors during activation."""
        # Mock existing scene
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state
        mock_hass.services.async_call.side_effect = HomeAssistantError("Activation failed")

        with pytest.raises(HomeAssistantError) as exc_info:
            await activate_scene(mock_hass, "test_scene")

        assert "Home Assistant error activating scene" in str(exc_info.value)

class TestListScenes:
    """Test list_scenes function."""

    @pytest.mark.asyncio
    async def test_list_scenes_success(self, mock_hass):
        """Test successful scene listing."""
        # Mock scene states
        mock_scene = Mock()
        mock_scene.entity_id = "scene.test_scene"
        mock_scene.state = "scening"
        mock_scene.attributes = {
            "friendly_name": "Test Scene",
            "last_activated": "2023-01-01T00:00:00"
        }

        mock_hass.states.async_all.return_value = [mock_scene]

        result = await list_scenes(mock_hass)

        assert "scenes" in result
        assert "count" in result
        assert result["count"] == 1
        assert len(result["scenes"]) == 1

        scene_info = result["scenes"][0]
        assert scene_info["entity_id"] == "scene.test_scene"
        assert scene_info["name"] == "Test Scene"
        assert scene_info["state"] == "scening"
        assert scene_info["last_activated"] == "2023-01-01T00:00:00"

    @pytest.mark.asyncio
    async def test_list_scenes_empty(self, mock_hass):
        """Test listing when no scenes exist."""
        mock_hass.states.async_all.return_value = []

        result = await list_scenes(mock_hass)

        assert result["count"] == 0
        assert result["scenes"] == []

    @pytest.mark.asyncio
    async def test_list_scenes_ha_error(self, mock_hass):
        """Test handling of Home Assistant errors in listing."""
        mock_hass.states.async_all.side_effect = HomeAssistantError("States error")

        with pytest.raises(HomeAssistantError) as exc_info:
            await list_scenes(mock_hass)

        assert "Home Assistant error listing scenes" in str(exc_info.value)
