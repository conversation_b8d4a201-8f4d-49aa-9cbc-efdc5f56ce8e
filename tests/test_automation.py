"""Tests for automation.py module."""
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

from automation import (
    create_automation,
    delete_automation,
    edit_automation,
    list_automations,
)


@pytest.fixture
def mock_hass():
    """Mock Home Assistant instance."""
    hass = Mock(spec=HomeAssistant)
    hass.services = Mock()
    hass.services.async_call = AsyncMock()
    hass.states = Mock()
    return hass

@pytest.fixture
def sample_automation_data():
    """Sample automation data for testing."""
    return {
        "alias": "Test Automation",
        "trigger": {"platform": "time", "at": "08:00:00"},
        "action": {"service": "light.turn_on", "entity_id": "light.living_room"}
    }

class TestCreateAutomation:
    """Test create_automation function."""

    @pytest.mark.asyncio
    async def test_create_automation_success(self, mock_hass, sample_automation_data):
        """Test successful automation creation."""
        result = await create_automation(mock_hass, sample_automation_data)

        assert result["status"] == "created"
        assert result["automation_id"] == "Test Automation"
        assert mock_hass.services.async_call.call_count == 2  # reload and turn_on

    @pytest.mark.asyncio
    async def test_create_automation_missing_alias(self, mock_hass):
        """Test automation creation with missing alias."""
        invalid_data = {"trigger": {}, "action": {}}

        with pytest.raises(ValueError) as exc_info:
            await create_automation(mock_hass, invalid_data)

        assert "Automation alias is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_automation_missing_trigger(self, mock_hass):
        """Test automation creation with missing trigger."""
        invalid_data = {"alias": "Test", "action": [{"service": "light.turn_on"}]}

        with pytest.raises(ValueError) as exc_info:
            await create_automation(mock_hass, invalid_data)

        assert "Automation trigger is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_automation_missing_action(self, mock_hass):
        """Test automation creation with missing action."""
        invalid_data = {"alias": "Test", "trigger": {}}

        with pytest.raises(ValueError) as exc_info:
            await create_automation(mock_hass, invalid_data)

        assert "Automation action is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_automation_ha_error(self, mock_hass, sample_automation_data):
        """Test handling of Home Assistant errors."""
        mock_hass.services.async_call.side_effect = HomeAssistantError("Service error")

        with pytest.raises(HomeAssistantError) as exc_info:
            await create_automation(mock_hass, sample_automation_data)

        assert "Home Assistant error creating automation" in str(exc_info.value)

class TestEditAutomation:
    """Test edit_automation function."""

    @pytest.mark.asyncio
    async def test_edit_automation_success(self, mock_hass):
        """Test successful automation editing."""
        # Mock existing automation
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        updates = {"trigger": {"platform": "time", "at": "09:00:00"}}
        result = await edit_automation(mock_hass, "test_automation", updates)

        assert result["status"] == "updated"
        assert result["automation_id"] == "test_automation"
        assert result["updates"] == updates

    @pytest.mark.asyncio
    async def test_edit_automation_not_found(self, mock_hass):
        """Test editing non-existent automation."""
        mock_hass.states.get.return_value = None

        with pytest.raises(HomeAssistantError) as exc_info:
            await edit_automation(mock_hass, "nonexistent", {})

        assert "Automation not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_edit_automation_empty_id(self, mock_hass):
        """Test editing with empty automation ID."""
        with pytest.raises(ValueError) as exc_info:
            await edit_automation(mock_hass, "", {})

        assert "Automation ID is required" in str(exc_info.value)

class TestDeleteAutomation:
    """Test delete_automation function."""

    @pytest.mark.asyncio
    async def test_delete_automation_success(self, mock_hass):
        """Test successful automation deletion."""
        # Mock existing automation
        mock_state = Mock()
        mock_hass.states.get.return_value = mock_state

        result = await delete_automation(mock_hass, "test_automation")

        assert result["status"] == "deleted"
        assert result["automation_id"] == "test_automation"
        assert mock_hass.services.async_call.call_count == 2  # turn_off and reload

    @pytest.mark.asyncio
    async def test_delete_automation_not_found(self, mock_hass):
        """Test deleting non-existent automation."""
        mock_hass.states.get.return_value = None

        with pytest.raises(HomeAssistantError) as exc_info:
            await delete_automation(mock_hass, "nonexistent")

        assert "Automation not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_automation_empty_id(self, mock_hass):
        """Test deleting with empty automation ID."""
        with pytest.raises(ValueError) as exc_info:
            await delete_automation(mock_hass, "")

        assert "Automation ID is required" in str(exc_info.value)

class TestListAutomations:
    """Test list_automations function."""

    @pytest.mark.asyncio
    async def test_list_automations_success(self, mock_hass):
        """Test successful automation listing."""
        # Mock automation states
        mock_automation = Mock()
        mock_automation.entity_id = "automation.test_automation"
        mock_automation.state = "on"
        mock_automation.attributes = {
            "friendly_name": "Test Automation",
            "last_triggered": "2023-01-01T00:00:00"
        }

        mock_hass.states.async_all.return_value = [mock_automation]

        result = await list_automations(mock_hass)

        assert "automations" in result
        assert "count" in result
        assert result["count"] == 1
        assert len(result["automations"]) == 1

        automation_info = result["automations"][0]
        assert automation_info["entity_id"] == "automation.test_automation"
        assert automation_info["alias"] == "Test Automation"
        assert automation_info["state"] == "on"
        assert automation_info["last_triggered"] == "2023-01-01T00:00:00"

    @pytest.mark.asyncio
    async def test_list_automations_empty(self, mock_hass):
        """Test listing when no automations exist."""
        mock_hass.states.async_all.return_value = []

        result = await list_automations(mock_hass)

        assert result["count"] == 0
        assert result["automations"] == []

    @pytest.mark.asyncio
    async def test_list_automations_ha_error(self, mock_hass):
        """Test handling of Home Assistant errors in listing."""
        mock_hass.states.async_all.side_effect = HomeAssistantError("States error")

        with pytest.raises(HomeAssistantError) as exc_info:
            await list_automations(mock_hass)

        assert "Home Assistant error listing automations" in str(exc_info.value)
