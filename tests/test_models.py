from hypothesis import given
from hypothesis import strategies as st

from backend.models import ThinClientActionV1


@given(
    action=st.text(min_size=1),
    data=st.dictionaries(st.text(), st.integers() | st.text() | st.floats() | st.none()),
    target_id=st.one_of(st.text(), st.none())
)
def test_thin_client_action_v1_valid(action, data, target_id):
    obj = ThinClientActionV1(action=action, data=data, target_id=target_id)
    assert obj.action == action
    assert obj.data == data
    assert obj.target_id == target_id

@given(
    action=st.text(min_size=1),
    data=st.dictionaries(st.text(), st.integers() | st.text() | st.floats() | st.none())
)
def test_thin_client_action_v1_required(action, data):
    obj = ThinClientActionV1(action=action, data=data)
    assert obj.action == action
    assert obj.data == data
    assert obj.target_id is None
