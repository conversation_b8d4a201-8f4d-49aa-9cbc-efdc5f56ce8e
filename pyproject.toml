[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sirohi-thin-client"
version = "1.0.0"
description = "A lightweight, secure client for Home Assistant automation with AI-powered natural language processing via Google Gemini"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Sirohi Labs", email = "<EMAIL>"}
]
maintainers = [
    {name = "Sirohi Labs", email = "<EMAIL>"}
]
keywords = ["home-assistant", "ai", "gemini", "automation", "smart-home"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Home Automation",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "aiohttp>=3.9.0",
    "python-dotenv>=1.0.0",
    "google-generativeai>=0.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "ruff>=0.1.9",
    "bandit[toml]>=1.7.5",
    "mypy>=1.7.0",
    "pre-commit>=3.6.0",
    "black>=23.12.0",
    "isort>=5.13.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
]

[project.urls]
Homepage = "https://github.com/sirohilabs/thin-client"
Documentation = "https://docs.sirohilabs.com/thin-client"
Repository = "https://github.com/sirohilabs/thin-client"
Issues = "https://github.com/sirohilabs/thin-client/issues"
Changelog = "https://github.com/sirohilabs/thin-client/blob/main/CHANGELOG.md"

[project.scripts]
thin-client = "backend.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["backend*", "tests*"]
exclude = ["docs*", "examples*"]

[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "S",   # bandit
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "B904",  # raise from within except (allow for now)
    "S101",  # use of assert detected (allow in tests)
    "S104",  # binding to all interfaces (allow for dev server)
    "S105",  # hardcoded passwords in tests
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101", "S105", "S106", "S108"]  # Allow assert, hardcoded passwords in tests
"backend/test_*.py" = ["S101", "S105", "S106", "S108"]
"examples/*" = ["B904", "S104"]  # Allow examples to be less strict

[tool.ruff.lint.isort]
known-first-party = ["backend", "tests"]

[tool.black]
target-version = ['py311']
line-length = 88
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "google.generativeai.*",
    "google.api_core.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests", "backend"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "security: marks tests as security tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
source = ["backend", "tests"]
omit = [
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "venv", "env"]
skips = ["B101"]  # Allow assert in tests

[tool.bandit.assert_used]
skips = ["*/test_*.py", "*/*_test.py"]
