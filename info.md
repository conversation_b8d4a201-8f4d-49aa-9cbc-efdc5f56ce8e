# Sirohi Thin Client for Home Assistant

[![HACS](https://img.shields.io/badge/HACS-Custom-orange.svg)](https://github.com/hacs/integration)
[![License](https://img.shields.io/github/license/griffin/thin-client)](LICENSE)

A secure, lightweight Home Assistant integration that enables natural language control of your smart home through Sirohi Labs' AI backend.

## Features

- 🗣️ **Natural Language Processing**: Control your smart home with plain English commands
- 🔒 **Security First**: HTTPS-only communication, license key authentication, sensitive data redaction
- ⚡ **Real-time Control**: Direct integration with Home Assistant services and entities
- 🏠 **Comprehensive Coverage**: Supports lights, switches, scenes, automations, and more
- 🛡️ **Timeout Protection**: 10-second timeout protection for all service calls
- 📊 **Origin Tracking**: All actions tagged with `thin_client` origin for audit trails

## Quick Start

### Installation

1. **Via HACS (Recommended)**:

   - Open HACS in Home Assistant
   - Go to "Integrations"
   - Click the three dots menu → "Custom repositories"
   - Add `https://tea.griffin.ovh/g/thin-client.git` as an Integration
   - Search for "Sirohi Thin Client" and install

2. **Manual Installation**:
   - Download the latest release
   - Copy the `custom_components/sirohi_thin_client` folder to your Home Assistant `custom_components` directory
   - Restart Home Assistant

### Configuration

1. **Add the Integration**:

   - Go to Settings → Devices & Services
   - Click "Add Integration"
   - Search for "Sirohi Thin Client"

2. **Configure Backend Connection**:

   - **Backend URL**: Your Sirohi Labs backend endpoint (HTTPS required)
   - **License Key**: Your Sirohi Labs license key

3. **Test the Integration**:

   ```yaml
   # Example service call
   service: sirohi_thin_client.send_prompt
   data:
     license_key: "your-license-key"
     prompt: "Turn on the kitchen lights"
   ```

## Example Commands

The integration supports natural language commands like:

- 💡 **Lighting**: "Turn on the kitchen lights", "Dim the bedroom lights to 30%"
- 🏠 **Scenes**: "Activate movie night scene", "Set romantic dinner lighting"
- 🔧 **Automations**: "Create a morning routine", "Disable the vacation automation"
- 🌡️ **Climate**: "Set the temperature to 22 degrees", "Turn on the air conditioning"
- 🔒 **Security**: "Lock all doors", "Check if the garage door is open"

## Security Features

### 🔒 Data Protection

- **HTTPS Only**: All communication uses encrypted HTTPS connections
- **License Key Authentication**: Secure authentication via HTTP headers
- **Sensitive Data Redaction**: Automatic redaction of passwords, tokens, and API keys in logs
- **Input Validation**: Comprehensive validation of all user inputs and backend responses

### 🛡️ Operational Security

- **Timeout Protection**: 10-second timeout on all service calls
- **Origin Tracking**: All actions tagged with `thin_client` origin for audit purposes
- **Error Sanitization**: Structured error messages prevent information leakage
- **Exponential Backoff**: Retry logic with jitter for resilience

## API Schema v0.2

The integration uses a versioned JSON schema for reliable communication:

```json
{
	"action": "call_service",
	"data": {
		"domain": "light",
		"service": "turn_on",
		"entity_id": "light.kitchen",
		"data": {
			"brightness": 255,
			"color_temp": 370
		}
	}
}
```

## Supported Actions

| Action              | Description                        | Example                          |
| ------------------- | ---------------------------------- | -------------------------------- |
| `call_service`      | Execute any Home Assistant service | Turn on lights, set temperature  |
| `create_automation` | Create new automations             | Morning routines, security rules |
| `edit_automation`   | Modify existing automations        | Update triggers, change actions  |
| `delete_automation` | Remove automations                 | Clean up old rules               |
| `create_scene`      | Create lighting/device scenes      | Movie night, dinner party        |
| `activate_scene`    | Activate saved scenes              | Set mood lighting                |
| `list_scenes`       | Get all available scenes           | Browse configurations            |

## Troubleshooting

### Common Issues

1. **"HTTPS protocol required" error**:

   - Ensure your backend URL starts with `https://`
   - Self-signed certificates are not supported

2. **"License key required" error**:

   - Verify your license key is correctly entered
   - Check for extra spaces or special characters

3. **Service call timeouts**:
   - Check Home Assistant logs for underlying service issues
   - Verify entity IDs exist and are accessible

### Debug Logging

Enable debug logging in your `configuration.yaml`:

```yaml
logger:
  default: info
  logs:
    custom_components.sirohi_thin_client: debug
```

## Support

- 🐛 **Issues**: [Local Issues](https://tea.griffin.ovh/g/thin-client/issues)
- 📧 **Email**: Local deployment support

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

Made with ❤️ for local deployment
