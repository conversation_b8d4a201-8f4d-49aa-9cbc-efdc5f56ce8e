#!/usr/bin/env python3
"""
Setup script for Sirohi Thin Client development environment.
Creates .env file from .env.example and provides guidance.
"""

import os
import shutil
import sys


def main():
    """Set up the development environment."""
    print("🔧 Sirohi Thin Client Environment Setup")
    print("=" * 50)

    # Check if .env already exists
    if os.path.exists('.env'):
        response = input("⚠️  .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("✅ Keeping existing .env file.")
            show_next_steps()
            return

    # Copy .env.example to .env
    if not os.path.exists('.env.example'):
        print("❌ .env.example file not found!")
        sys.exit(1)

    shutil.copy('.env.example', '.env')
    print("✅ Created .env file from .env.example")

    print("\n📝 Next steps:")
    print("1. Edit .env file and add your actual API keys:")
    print("   - Get Gemini API key from: https://aistudio.google.com/app/apikey")
    print("   - Add your Home Assistant long-lived access token")
    print("   - Set your backend URL (if testing against real backend)")

    print("\n2. Install dependencies:")
    print("   pip install -r requirements.txt")
    print("   pip install python-dotenv  # For loading .env files")

    print("\n3. Run tests:")
    print("   pytest -v  # Run all tests")
    print("   pytest backend/test_gemini_client.py -v  # Test Gemini integration")

    show_next_steps()

def show_next_steps():
    """Show additional setup information."""
    print("\n🔐 Security Notes:")
    print("- Never commit .env file to git (it's in .gitignore)")
    print("- Use environment variables in production")
    print("- Rotate API keys regularly")

    print("\n🧪 Testing with real API:")
    print("- Set INTEGRATION_TESTS=true in .env to enable real API tests")
    print("- Real API tests will consume your Gemini quota")
    print("- Mock tests run by default (no API key needed)")

if __name__ == "__main__":
    main()
