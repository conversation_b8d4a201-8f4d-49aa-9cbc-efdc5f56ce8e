name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  lint-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          pip install -r requirements-dev.txt
          pip install -e .
      - name: Ruff
        run: ruff check .
      - name: Bandit
        run: bandit -r . -lll
      - name: Mypy
        run: mypy --strict thin_client backend
      - name: Pytest
        run: pytest
      - name: Hassfest
        run: hassfest test
