
==================================== ERRORS ====================================
________________ ERROR collecting backend/test_gemini_client.py ________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/backend/test_gemini_client.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
backend/test_gemini_client.py:3: in <module>
    import pytest_asyncio
E   ModuleNotFoundError: No module named 'pytest_asyncio'
______________________ ERROR collecting tests/test_api.py ______________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/tests/test_api.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_api.py:3: in <module>
    import aiohttp
E   ModuleNotFoundError: No module named 'aiohttp'
__________________ ERROR collecting tests/test_automation.py ___________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/tests/test_automation.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_automation.py:4: in <module>
    from homeassistant.core import HomeAssistant
E   ModuleNotFoundError: No module named 'homeassistant'
____________________ ERROR collecting tests/test_context.py ____________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/tests/test_context.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_context.py:4: in <module>
    from homeassistant.core import HomeAssistant
E   ModuleNotFoundError: No module named 'homeassistant'
________________ ERROR collecting tests/test_entity_control.py _________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/tests/test_entity_control.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_entity_control.py:5: in <module>
    from homeassistant.core import HomeAssistant
E   ModuleNotFoundError: No module named 'homeassistant'
_____________________ ERROR collecting tests/test_scene.py _____________________
ImportError while importing test module '/Users/<USER>/Documents/Sirohi Labs/Dev/thin-client/tests/test_scene.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_scene.py:4: in <module>
    from homeassistant.core import HomeAssistant
E   ModuleNotFoundError: No module named 'homeassistant'
=========================== short test summary info ============================
ERROR backend/test_gemini_client.py
ERROR tests/test_api.py
ERROR tests/test_automation.py
ERROR tests/test_context.py
ERROR tests/test_entity_control.py
ERROR tests/test_scene.py
!!!!!!!!!!!!!!!!!!! Interrupted: 6 errors during collection !!!!!!!!!!!!!!!!!!!!
6 errors in 0.09s
